import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
import time
import sys
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model import Kronos, KronosTokenizer, KronosPredictor


class BinanceDataFetcher:
    """币安数据获取器 - 中国可访问"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com"
        # 备用URL（如果主URL不可访问）
        self.backup_urls = [
            "https://api1.binance.com",
            "https://api2.binance.com", 
            "https://api3.binance.com"
        ]
        
    def get_klines(self, symbol="ETHUSDT", interval="15m", limit=500):
        """
        获取K线数据
        
        Args:
            symbol: 交易对符号 (如 ETHUSDT)
            interval: 时间间隔 (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            limit: 数据条数 (最大1000)
        """
        endpoint = "/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        # 尝试多个URL
        for url in [self.base_url] + self.backup_urls:
            try:
                response = requests.get(url + endpoint, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    return self._parse_klines(data)
                else:
                    print(f"API返回错误: {response.status_code}")
            except Exception as e:
                print(f"尝试URL {url} 失败: {e}")
                continue
        
        raise Exception("所有API URL都无法访问，请检查网络连接")
    
    def _parse_klines(self, raw_data):
        """解析K线数据"""
        df = pd.DataFrame(raw_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        # 转换数据类型
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'quote_asset_volume']:
            df[col] = df[col].astype(float)
        
        # 重命名列以匹配Kronos格式
        df = df.rename(columns={
            'timestamp': 'timestamps',
            'quote_asset_volume': 'amount'
        })
        
        # 选择需要的列
        df = df[['timestamps', 'open', 'high', 'low', 'close', 'volume', 'amount']]
        
        return df.sort_values('timestamps').reset_index(drop=True)


def plot_prediction_with_realtime(historical_df, pred_df, symbol="ETH-USDT"):
    """绘制实时预测结果"""
    # 设置预测数据的时间索引
    pred_df.index = historical_df.index[-pred_df.shape[0]:]
    
    # 准备数据
    hist_close = historical_df['close']
    pred_close = pred_df['close']
    hist_volume = historical_df['volume']
    pred_volume = pred_df['volume']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
    
    # 价格图
    ax1.plot(hist_close.index, hist_close.values, label='历史价格', color='blue', linewidth=1.5)
    ax1.plot(pred_close.index, pred_close.values, label='预测价格', color='red', linewidth=2, linestyle='--')
    ax1.set_ylabel('价格 (USDT)', fontsize=12)
    ax1.set_title(f'{symbol} 实时预测结果', fontsize=14, fontweight='bold')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 成交量图
    ax2.plot(hist_volume.index, hist_volume.values, label='历史成交量', color='blue', linewidth=1.5)
    ax2.plot(pred_volume.index, pred_volume.values, label='预测成交量', color='red', linewidth=2, linestyle='--')
    ax2.set_ylabel('成交量 (ETH)', fontsize=12)
    ax2.set_xlabel('时间', fontsize=12)
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"eth_usdt_prediction_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"预测图表已保存为: {filename}")
    
    plt.show()


def main():
    print("🚀 ETH-USDT 实时预测系统启动")
    print("=" * 50)
    
    # 1. 初始化数据获取器
    print("📡 初始化数据获取器...")
    fetcher = BinanceDataFetcher()
    
    # 2. 获取实时数据
    print("📊 获取ETH-USDT实时数据...")
    try:
        df = fetcher.get_klines(symbol="ETHUSDT", interval="5m", limit=400)
        print(f"✅ 成功获取 {len(df)} 条数据")
        print(f"📅 数据时间范围: {df['timestamps'].min()} 到 {df['timestamps'].max()}")
        print(f"💰 当前价格: ${df['close'].iloc[-1]:.2f}")
        print(f"📈 24h涨跌: {((df['close'].iloc[-1] / df['close'].iloc[-288] - 1) * 100):.2f}%")
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return
    
    # 3. 加载模型
    print("\n🤖 加载Kronos模型...")
    try:
        tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
        model = Kronos.from_pretrained("NeoQuasar/Kronos-base")
        predictor = KronosPredictor(model, tokenizer, device="cpu", max_context=512)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 4. 准备预测数据
    print("\n📋 准备预测数据...")
    lookback = 400  # 使用400个历史数据点
    pred_len = 24   # 预测未来24个时间点（2小时）
    
    if len(df) < lookback:
        print(f"❌ 数据不足，需要至少{lookback}条数据，当前只有{len(df)}条")
        return
    
    # 选择最近的数据
    recent_df = df.tail(lookback + pred_len).copy()
    
    # 历史数据
    x_df = recent_df.iloc[:lookback][['open', 'high', 'low', 'close', 'volume', 'amount']]
    x_timestamp = recent_df.iloc[:lookback]['timestamps']
    
    # 生成未来时间戳（5分钟间隔）
    last_time = x_timestamp.iloc[-1]
    future_timestamps = pd.date_range(
        start=last_time + timedelta(minutes=5),
        periods=pred_len,
        freq='5T'
    )
    # 转换为pandas Series以匹配模型期望的格式
    future_timestamps = pd.Series(future_timestamps)
    
    print(f"📊 使用 {len(x_df)} 条历史数据")
    print(f"🔮 预测未来 {pred_len} 个时间点（{pred_len*5//60:.1f}小时）")
    print(f"⏰ 预测时间范围: {future_timestamps.iloc[0]} 到 {future_timestamps.iloc[-1]}")
    
    # 5. 执行预测
    print("\n🔮 开始预测...")
    start_time = time.time()
    
    try:
        pred_df = predictor.predict(
            df=x_df,
            x_timestamp=x_timestamp,
            y_timestamp=future_timestamps,
            pred_len=pred_len,
            T=0.8,          # 降低温度以获得更稳定的预测
            top_p=0.9,      # 核采样
            sample_count=3, # 多次采样取平均
            verbose=True
        )
        
        prediction_time = time.time() - start_time
        print(f"✅ 预测完成，耗时: {prediction_time:.1f}秒")
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return
    
    # 6. 分析预测结果
    print("\n📈 预测结果分析:")
    print("=" * 30)
    
    current_price = x_df['close'].iloc[-1]
    pred_prices = pred_df['close']
    
    print(f"当前价格: ${current_price:.2f}")
    print(f"30分钟后预测: ${pred_prices.iloc[5]:.2f} ({((pred_prices.iloc[5]/current_price-1)*100):+.2f}%)")
    print(f"1小时后预测: ${pred_prices.iloc[11]:.2f} ({((pred_prices.iloc[11]/current_price-1)*100):+.2f}%)")
    print(f"2小时后预测: ${pred_prices.iloc[-1]:.2f} ({((pred_prices.iloc[-1]/current_price-1)*100):+.2f}%)")
    
    # 价格趋势分析
    price_trend = "上涨" if pred_prices.iloc[-1] > current_price else "下跌"
    max_price = pred_prices.max()
    min_price = pred_prices.min()
    
    print(f"\n📊 趋势分析:")
    print(f"整体趋势: {price_trend}")
    print(f"预测最高价: ${max_price:.2f}")
    print(f"预测最低价: ${min_price:.2f}")
    print(f"价格波动范围: {((max_price-min_price)/current_price*100):.2f}%")
    
    # 7. 可视化结果
    print("\n📊 生成预测图表...")
    try:
        plot_prediction_with_realtime(recent_df.iloc[:lookback], pred_df, "ETH-USDT")
    except Exception as e:
        print(f"⚠️ 图表生成失败: {e}")
    
    # 8. 保存预测结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"eth_usdt_prediction_{timestamp}.csv"
    pred_df.to_csv(result_file)
    print(f"💾 预测结果已保存为: {result_file}")
    
    print("\n🎉 预测完成！")
    print("⚠️  风险提示: 此预测仅供参考，不构成投资建议！")


if __name__ == "__main__":
    main()
